defmodule Repobot.Files do
  require Logger

  @moduledoc """
  Functions for working with repository files and calculating similarities.
  """

  @default_exclusions %{
    # Exact match files
    files: [
      ".gitkeep",
      ".DS_Store",
      "Thumbs.db",
      "mix.lock",
      "package-lock.json",
      "yarn.lock",
      "pnpm-lock.yaml",
      "Gemfile.lock",
      "composer.lock",
      "Pipfile.lock",
      "poetry.lock"
    ],
    # Directory prefixes
    directories: [
      "_build/",
      "deps/",
      "priv/static/",
      "node_modules/",
      "vendor/",
      "logs/",
      ".vscode/",
      ".idea/"
    ]
  }

  @doc """
  Find files that exist in at least two repositories with identical paths,
  excluding common noise files and directories.
  Returns a list of maps containing file information and counts.

  ## Parameters
    * repositories - List of repository structs with preloaded files
    * opts - Optional keyword list of options:
      * :min_repos - Minimum number of repositories a file must exist in (default: 2)
      * :exclusions - List of additional glob patterns to exclude (appended to defaults)

  ## Returns
    * {:ok, [%{path: String.t(), size: integer(), count: integer(), total_repos: integer()}]}
      - List of common files with counts.
    * {:error, String.t()} - Error message if something went wrong.
  """
  def find_common_files(repositories, opts \\ []) do
    min_repos = Keyword.get(opts, :min_repos, 2)
    total_repos = length(repositories)

    # Combine default and user-provided exclusions
    custom_exclusions = Keyword.get(opts, :exclusions, [])

    all_file_exclusions =
      @default_exclusions.files ++ Enum.filter(custom_exclusions, &(!String.ends_with?(&1, "/")))

    all_dir_exclusions =
      @default_exclusions.directories ++
        Enum.filter(custom_exclusions, &String.ends_with?(&1, "/"))

    try do
      # 1. Get all unique FILE paths from all repositories
      all_file_paths =
        repositories
        |> Enum.flat_map(& &1.files)
        |> Enum.filter(&(&1.type == "file"))
        |> Enum.map(& &1.path)
        |> Enum.uniq()

      # 2. Filter out excluded paths
      filtered_paths =
        all_file_paths
        |> Enum.reject(fn path ->
          filename = Path.basename(path)
          # Check if filename matches any excluded files (regardless of directory)
          # Check if path starts with any excluded directory
          # Check for .log files
          # Check for custom glob patterns
          filename in all_file_exclusions or
            Enum.any?(all_dir_exclusions, &String.starts_with?(path, &1)) or
            String.ends_with?(path, ".log") or
            Enum.any?(custom_exclusions, fn pattern ->
              case String.split(pattern, "*", parts: 2) do
                [prefix, suffix] ->
                  String.starts_with?(path, prefix) and String.ends_with?(path, suffix)

                [exact] ->
                  path == exact or String.starts_with?(path, exact <> "/")
              end
            end)
        end)

      # 3. Count occurrences for each remaining path and filter by min_repos
      # Use filtered list
      common_files =
        filtered_paths
        |> Enum.map(fn path ->
          count =
            Enum.count(repositories, fn repo ->
              Enum.any?(repo.files, &(&1.path == path && &1.type == "file"))
            end)

          # Get file info from first repo that has it
          first_file =
            repositories
            |> Enum.find_value(fn repo ->
              Enum.find(repo.files, &(&1.path == path && &1.type == "file"))
            end)

          {path, count, first_file}
        end)
        |> Enum.filter(fn {_path, count, _file} -> count >= min_repos end)
        |> Enum.map(fn {path, count, file} ->
          %{
            "path" => path,
            "size" => file.size,
            "count" => count,
            "total_repos" => total_repos
          }
        end)
        |> Enum.sort_by(&{-&1["count"], &1["path"]})

      Logger.info("Found #{length(common_files)} common files in >= #{min_repos} repos.")
      {:ok, common_files}
    rescue
      e ->
        Logger.error("Error finding common files: #{Exception.format(:error, e, __STACKTRACE__)}")
        {:error, "Failed to find common files: #{Exception.message(e)}"}
    end
  end

  @doc """
  Calculate similarity between multiple file contents as a percentage.
  Returns 100 if there is only one content or all contents are identical.
  Returns a percentage between 0 and 100 based on line-by-line comparison.
  """
  @spec calculate_similarity([String.t()]) :: non_neg_integer()
  def calculate_similarity(contents) when length(contents) <= 1, do: 100

  def calculate_similarity([content1, content2]),
    do: calculate_pair_similarity(content1, content2)

  def calculate_similarity(contents) do
    unique_contents = Enum.uniq(contents)

    # If all contents are identical
    if length(unique_contents) == 1 do
      100
    else
      # Find the most frequent content string
      {most_frequent_content, _count} =
        contents
        |> Enum.frequencies()
        |> Enum.max_by(fn {_content, count} -> count end)

      # Calculate similarity of each content to the most frequent one
      total_similarity_to_most_frequent =
        contents
        |> Enum.map(&calculate_pair_similarity(&1, most_frequent_content))
        |> Enum.sum()

      # Calculate the average similarity to the most frequent version
      trunc(total_similarity_to_most_frequent / length(contents))
    end
  end

  @doc """
  Calculate similarity between two file contents as a percentage.
  Returns 0 if either content is nil.
  Returns 100 if contents are identical.
  Returns a percentage between 0 and 100 based on line-by-line comparison.
  """
  @spec calculate_pair_similarity(String.t() | nil, String.t() | nil) :: non_neg_integer()
  def calculate_pair_similarity(nil, _content2), do: 0
  def calculate_pair_similarity(_content1, nil), do: 0

  def calculate_pair_similarity(content1, content2) do
    if content1 == content2 do
      100
    else
      # Normalize line endings to \n and split into lines
      lines1 =
        content1
        |> String.replace("\r\n", "\n")
        |> String.split("\n", trim: true)

      lines2 =
        content2
        |> String.replace("\r\n", "\n")
        |> String.split("\n", trim: true)

      # Get total number of lines
      total_lines = max(length(lines1), length(lines2))

      if total_lines > 0 do
        # Count matching lines
        matching_count =
          Enum.zip(lines1, lines2)
          |> Enum.count(fn {l1, l2} -> l1 == l2 end)

        # Calculate percentage
        trunc(matching_count * 100 / total_lines)
      else
        100
      end
    end
  end

  @doc """
  Find the most similar pair of repositories based on their file contents.
  Returns nil if there are fewer than 2 repositories.
  Returns {repo1_name, repo1_data, repo2_name, repo2_data} for the most similar pair.
  """
  @spec find_most_similar_pair(%{String.t() => map()}) ::
          {String.t(), map(), String.t(), map()} | nil
  def find_most_similar_pair(repo_contents) when map_size(repo_contents) < 2, do: nil

  def find_most_similar_pair(repo_contents) do
    repo_pairs =
      for {repo1, data1} <- repo_contents,
          {repo2, data2} <- repo_contents,
          repo1 < repo2 do
        similarity = calculate_pair_similarity(data1["content"], data2["content"])
        {similarity, repo1, data1, repo2, data2}
      end

    case Enum.sort_by(repo_pairs, fn {similarity, _, _, _, _} -> -similarity end) do
      [{_similarity, repo1, data1, repo2, data2} | _] -> {repo1, data1, repo2, data2}
      _ -> nil
    end
  end

  @doc """
  Calculate similarity for a list of common files across repositories.
  Takes a list of repositories and a list of common files (from find_common_files/2).
  Returns a list of files with similarity scores added.

  ## Messages sent to receiver_pid:
  - {:similarity_progress, progress} - Progress from 0 to 100
  - {:similarity_complete, files} - When all similarities are calculated
  - {:similarity_error, reason} - If calculation fails
  """
  @spec calculate_common_files_similarity(
          [map()],
          [Repobot.Repository.t()],
          pid() | {pid(), atom()}
        ) :: :ok
  def calculate_common_files_similarity(common_files, repositories, receiver) do
    total_files = length(common_files)

    if total_files > 0 do
      Task.start_link(fn ->
        try do
          result =
            common_files
            |> Stream.with_index(1)
            |> Enum.reduce_while({:ok, []}, fn {file, index}, {:ok, acc} ->
              # Calculate and send progress
              progress = floor(index * 100 / total_files)
              send_to_receiver(receiver, {:similarity_progress, progress})

              # Get file content from each repository that has this file
              # Get list of {content, repo_id} tuples
              contents_with_repo_info =
                repositories
                |> Enum.map(fn repo ->
                  case Enum.find(repo.files, &(&1.path == file["path"] && &1.type == "file")) do
                    nil -> nil
                    repo_file -> {repo_file.content, repo.id}
                  end
                end)
                |> Enum.reject(&is_nil/1)

              contents =
                Enum.map(contents_with_repo_info, &elem(&1, 0))
                |> Enum.reject(&is_nil/1)

              # Get the actual content from the first available source (they should be identical if similarity is 100)
              # Find the first non-nil content from the list
              actual_content =
                Enum.find_value(contents_with_repo_info, fn {content, _repo_id} -> content end)

              # Skip files where all content is nil
              if Enum.empty?(contents) do
                # All repositories have nil content for this file, skip it
                {:cont, {:ok, acc}}
              else
                # Calculate similarity only if we have content from at least 2 repos
                similarity =
                  if length(contents) >= 2 do
                    calculate_similarity(contents)
                  else
                    # If only 1 repo has the file (or 0 with content), similarity is 100 (or undefined? Let's use 100)
                    100
                  end

                # Start with original map %{"path" => ..., "count" => ...}
                file_with_similarity_and_content =
                  file
                  |> Map.put("similarity", similarity)
                  # Add the actual content back
                  |> Map.put("content", actual_content)
                  # Add source repo ID
                  |> Map.put("source_repo_id", elem(List.first(contents_with_repo_info), 1))

                {:cont, {:ok, [file_with_similarity_and_content | acc]}}
              end
            end)

          case result do
            {:ok, files} ->
              files =
                files
                |> Enum.reverse()
                |> Enum.sort_by(&{-&1["similarity"], -&1["count"], &1["path"]})

              send_to_receiver(receiver, {:similarity_complete, files})

            {:error, reason} ->
              send_to_receiver(receiver, {:similarity_error, reason})
          end
        rescue
          e ->
            Logger.error(
              "Error calculating similarity: #{Exception.format(:error, e, __STACKTRACE__)}"
            )

            send_to_receiver(receiver, {:similarity_error, Exception.message(e)})
        end
      end)
    else
      send_to_receiver(receiver, {:similarity_complete, []})
    end

    :ok
  end

  # Helper function to send messages to the receiver based on its format
  defp send_to_receiver(receiver_pid, message) when is_pid(receiver_pid) do
    send(receiver_pid, message)
  end

  defp send_to_receiver({pid, component_name}, message)
       when is_pid(pid) and is_atom(component_name) do
    send(pid, {:files, component_name, message})
  end
end
