defmodule Repobot.AI.OpenAI do
  @moduledoc """
  OpenAI implementation of the Repobot.AI.Behaviour.
  Uses OpenAI's API to provide AI features.
  """

  @behaviour Repobot.AI.Behaviour

  require Logger

  @impl true
  def infer_tags(source_file, _organization) do
    with {:ok, api_key} <- get_api_key(),
         {:ok, prompt} <- build_prompt(source_file),
         {:ok, response} <- call_openai(prompt, api_key) do
      parse_tags(response, source_file)
    end
  end

  @impl true
  def infer_categories(_source_files, _organization) do
    {:ok, %{}}
  end

  @impl true
  def generate_template(file1, file2, file_path, available_variables, _organization) do
    with {:ok, api_key} <- get_api_key(),
         {:ok, prompt} <- build_template_prompt(file1, file2, file_path, available_variables),
         {:ok, response} <- call_openai(prompt, api_key) do
      # Strip out any Markdown code fences and language identifiers
      template =
        response
        |> String.trim()
        # Handle optional language identifier with any whitespace
        |> String.replace(~r/^```(?:yaml|liquid)?\s*\n/, "")
        # Handle any trailing whitespace
        |> String.replace(~r/\n```\s*$/, "")
        # Remove any remaining whitespace
        |> String.trim()

      {:ok, template}
    end
  end

  defp get_api_key do
    case System.get_env("OPENAI_API_KEY") do
      nil -> {:error, "OPENAI_API_KEY environment variable is not set"}
      key -> {:ok, key}
    end
  end

  defp build_prompt(source_file) do
    # Get repository languages
    languages =
      source_file.repositories
      |> Enum.map(& &1.language)
      |> Enum.uniq()
      |> Enum.reject(&is_nil(&1))
      |> Enum.join(", ")

    # Get folder names to exclude from tags
    folder_names =
      source_file.repositories
      |> Enum.map(& &1.folder.name)
      |> Enum.uniq()
      |> Enum.join(", ")

    # Get all existing tags for the organization
    existing_tags =
      Repobot.Tags.list_organization_tag_names(source_file.organization_id)
      |> Enum.join(", ")

    prompt = """
    Please analyze this source file and suggest appropriate category tags.
    Consider the following information:

    File name: #{source_file.name}
    Target path: #{source_file.target_path}
    Repository languages: #{languages}

    Existing tags in the system: #{existing_tags}

    IMPORTANT RULES:
    1. STRONGLY PREFER using existing tags from the list above
    2. Only suggest new tags if none of the existing tags are suitable
    3. DO NOT suggest tags that match these folder names: #{folder_names}
    4. Return EXACTLY 3 most relevant tags, no more, no less

    Each tag MUST:
    - Be lowercase
    - Use hyphens to separate words (e.g. github-actions, continuous-integration)
    - Contain no special characters except hyphens
    - Be either a single word or hyphenated compound words

    Return only the tags, one per line, no explanation.
    """

    {:ok, prompt}
  end

  defp call_openai(prompt, api_key) do
    url = "https://api.openai.com/v1/chat/completions"

    headers = [
      {"Authorization", "Bearer #{api_key}"},
      {"Content-Type", "application/json"}
    ]

    body = %{
      model: "gpt-3.5-turbo",
      messages: [
        %{
          role: "system",
          content:
            "You are a code analysis assistant that suggests tags for source files. Respond only with relevant tags, one per line. Never suggest tags that match the provided folder names."
        },
        %{
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3
    }

    case Req.post(url, json: body, headers: headers) do
      {:ok, %{status: 200, body: %{"choices" => [%{"message" => %{"content" => content}} | _]}}} ->
        {:ok, content}

      {:ok, %{status: status, body: body}} ->
        Logger.error("OpenAI API error: #{status} - #{inspect(body)}")
        {:error, "OpenAI API error: #{status}"}

      {:error, error} ->
        Logger.error("OpenAI API request failed: #{inspect(error)}")
        {:error, "Failed to call OpenAI API"}
    end
  end

  defp parse_tags(response, source_file) do
    # Get folder names for filtering
    folder_names =
      source_file.repositories
      |> Enum.map(&String.downcase(&1.folder.name))
      |> MapSet.new()

    # Get tags from response and filter out any that match folder names
    tags =
      response
      |> String.split("\n")
      |> Enum.map(&String.trim/1)
      |> Enum.reject(fn tag ->
        # Reject empty lines, folder names, and lines that don't match tag format
        # Only accept tags that are lowercase and contain only letters, numbers, and hyphens
        tag == "" ||
          MapSet.member?(folder_names, String.downcase(tag)) ||
          !Regex.match?(~r/^[a-z0-9-]+$/, tag)
      end)
      |> Enum.take(5)

    {:ok, tags}
  end

  defp build_template_prompt(file1, file2, file_path, available_variables) do
    vars_info =
      available_variables
      |> Enum.map(fn {var, description} ->
        "#{var}: #{description}"
      end)
      |> Enum.join("\n")

    # Determine if this is a GitHub Actions workflow file
    is_workflow = String.contains?(file_path, ".github/workflows/")

    prompt =
      if is_workflow do
        build_github_actions_prompt(file1, file2, vars_info)
      else
        build_generic_prompt(file1, file2, vars_info)
      end

    {:ok, prompt}
  end

  defp build_github_actions_prompt(file1, file2, vars_info) do
    """
    You are a template generator specializing in GitHub Actions workflows.
    Your task is to create a Liquid template that can generate both of these workflow files.

    File 1:
    ```yaml
    #{file1}
    ```

    File 2:
    ```yaml
    #{file2}
    ```

    Available variables:
    #{vars_info}

    STRICT REQUIREMENTS FOR GITHUB ACTIONS WORKFLOWS:
    1. For matrix configurations in strategy:
       - ALWAYS replace version arrays with settings variables using loops
       - For Elixir versions: settings.elixir_versions
       - For OTP versions: settings.otp_versions
       - KEEP the YAML list structure with proper indentation
       - DO NOT add any conditional logic to matrix values
       - DO NOT modify the matrix structure

    Example - REQUIRED transformation:
    Input:
        strategy:
          matrix:
            elixir:
              - "1.14.5"
              - "1.15.7"
              - "1.16.3"
            otp:
              - "26.2"
              - "26.1"
              - "25.3"

    Output:
        strategy:
          matrix:
            elixir:
              {% for version in settings.elixir_versions -%}
              - "{{ version }}"
              {% endfor %}
            otp:
              {% for version in settings.otp_versions -%}
              - "{{ version }}"
              {% endfor %}

    FORBIDDEN PATTERNS:
    1. DO NOT use inline array syntax
    2. DO NOT add conditionals to matrix values
    3. DO NOT modify any other parts of the workflow unless they differ between files
    4. DO NOT change job names, step names, or other structural elements
    5. DO NOT add comments or explanations in the template

    Return only the template content with no explanation or additional text.
    """
  end

  defp build_generic_prompt(file1, file2, vars_info) do
    """
    Please analyze these two similar files and create a Liquid template that can generate both versions.
    Use the available variables to make the template dynamic where appropriate.

    File 1:
    ```
    #{file1}
    ```

    File 2:
    ```
    #{file2}
    ```

    Available variables:
    #{vars_info}

    Rules for template generation:
    1. Use Liquid template syntax (e.g., {{ variable }}, {% if condition %}, {% for item in items %})
    2. Only use the variables listed above
    3. Keep the template as simple as possible while capturing the differences
    4. Preserve all common parts exactly as they appear
    5. Add comments to explain complex logic
    6. Handle whitespace appropriately

    Return only the template content, no explanation or additional text.
    """
  end
end
