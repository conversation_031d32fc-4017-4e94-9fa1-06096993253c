defmodule RepobotWeb.Live.Onboarding.Steps.RepositorySync do
  use RepobotWeb.Live.Onboarding.Step

  alias Repobot.Repositories

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_new(:selected_repos, fn -> MapSet.new() end)
      |> assign_new(:repository_filter, fn -> "" end)
      |> assign_new(:refreshing, fn -> false end)
      |> maybe_load_repositories()
      |> assign_repositories()
      |> maybe_filter_repos()

    {:ok, socket}
  end

  @doc """
  Renders the repository sync step of the onboarding process.
  """
  def render(assigns) do
    ~H"""
    <div>
      <h2 class="text-2xl font-semibold text-slate-900 mb-4">Repository Synchronization</h2>
      <div>
        <div class="mb-6 bg-slate-50 rounded-lg border border-slate-200 p-4">
          <div class="flex items-start gap-2">
            <.icon name="hero-template" class="w-5 h-5 text-slate-400 mt-0.5 flex-shrink-0" />
            <div>
              <h3 class="text-sm font-medium text-slate-900">Template Repository</h3>
              <p class="mt-1 text-sm text-slate-600">
                {@state.template_repo.full_name}
              </p>
            </div>
          </div>
        </div>

        <p class="text-slate-600 mb-6">
          Select the repositories you want to manage with RepoBot. You can always add or remove repositories later.
        </p>

        <%= if @refreshing do %>
          <div class="flex flex-col items-center justify-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <span class="ml-3 text-sm text-slate-600">Loading repositories...</span>
          </div>
        <% else %>
          <div class="mb-4">
            <label for="repository_filter" class="block text-sm font-medium text-slate-700">
              Filter Repositories
            </label>
            <div class="mt-1">
              <input
                type="text"
                name="repository_filter"
                id="repository_filter"
                class="input block"
                placeholder="Type to filter repositories..."
                value={@repository_filter}
                phx-keyup="filter"
                phx-debounce="300"
                phx-target={@myself}
              />
            </div>
          </div>

          <div class="space-y-6 max-h-[60vh] overflow-y-auto px-2">
            <%= for folder <- Enum.sort_by(@folders, fn folder ->
                    # Add sorting logic if needed, simplified for now
                    folder.name
                  end) do %>
              <% folder_repos = Map.get(@repos_by_folder, folder.id, []) %>
              <%= if Enum.any?(folder_repos) do %>
                <div class="bg-white rounded-lg border border-slate-200 overflow-hidden">
                  <div class="bg-slate-50 px-6 py-3 border-b border-slate-200">
                    <h3 class="text-lg font-medium text-slate-900 flex items-center gap-2">
                      <.icon name="hero-folder" class="w-5 h-5 text-slate-400" />
                      {folder.name}
                      <span class="text-sm text-slate-500">
                        ({length(folder_repos)} repositories)
                      </span>
                    </h3>
                  </div>
                  <div class="divide-y divide-slate-200">
                    <%= for repo <- folder_repos do %>
                      <%= if repo.id != @state.template_repo.id do %>
                        <div class="px-6 py-4 flex items-center justify-between hover:bg-slate-50">
                          <div class="flex-1 min-w-0 flex items-center gap-2">
                            <button
                              type="button"
                              phx-click="toggle_repository"
                              phx-value-id={repo.id}
                              phx-target={@myself}
                              class={[
                                "flex items-center gap-2 text-left",
                                if MapSet.member?(@selected_repos, repo.id) do
                                  "text-indigo-600 font-medium"
                                else
                                  "text-slate-700"
                                end
                              ]}
                            >
                              <div class={[
                                "h-5 w-5 rounded border flex items-center justify-center",
                                if MapSet.member?(@selected_repos, repo.id) do
                                  "bg-indigo-600 border-indigo-600"
                                else
                                  "border-slate-300"
                                end
                              ]}>
                                <%= if MapSet.member?(@selected_repos, repo.id) do %>
                                  <.icon name="hero-check" class="h-4 w-4 text-white" />
                                <% end %>
                              </div>
                              <span class="truncate">{repo.full_name}</span>
                            </button>
                            <%= if repo.fork do %>
                              <.icon name="hero-arrow-path" class="w-4 h-4 text-slate-400" />
                            <% end %>
                          </div>
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                </div>
              <% end %>
            <% end %>

            <%= if Enum.any?(@unorganized_repos) do %>
              <div class="bg-white rounded-lg border border-slate-200 overflow-hidden">
                <div class="bg-slate-50 px-6 py-3 border-b border-slate-200">
                  <h3 class="text-lg font-medium text-slate-900 flex items-center gap-2">
                    Unorganized Repositories
                    <span class="text-sm text-slate-500">
                      ({length(@unorganized_repos)} repositories)
                    </span>
                  </h3>
                </div>
                <div class="divide-y divide-slate-200">
                  <%= for repo <- @unorganized_repos do %>
                    <%= if repo.id != @state.template_repo.id do %>
                      <div class="px-6 py-4 flex items-center justify-between hover:bg-slate-50">
                        <div class="flex-1 min-w-0 flex items-center gap-2">
                          <button
                            type="button"
                            phx-click="toggle_repository"
                            phx-value-id={repo.id}
                            phx-target={@myself}
                            class={[
                              "flex items-center gap-2 text-left",
                              if MapSet.member?(@selected_repos, repo.id) do
                                "text-indigo-600 font-medium"
                              else
                                "text-slate-700"
                              end
                            ]}
                          >
                            <div class={[
                              "h-5 w-5 rounded border flex items-center justify-center",
                              if MapSet.member?(@selected_repos, repo.id) do
                                "bg-indigo-600 border-indigo-600"
                              else
                                "border-slate-300"
                              end
                            ]}>
                              <%= if MapSet.member?(@selected_repos, repo.id) do %>
                                <.icon name="hero-check" class="h-4 w-4 text-white" />
                              <% end %>
                            </div>
                            <span class="truncate">{repo.full_name}</span>
                          </button>
                          <%= if repo.fork do %>
                            <.icon name="hero-arrow-path" class="w-4 h-4 text-slate-400" />
                          <% end %>
                        </div>
                      </div>
                    <% end %>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  def handle_event("toggle_repository", %{"id" => id}, socket) do
    repositories = socket.assigns.repositories
    selected = MapSet.member?(socket.assigns.selected_repos, id)

    selected_repos =
      if selected do
        MapSet.delete(socket.assigns.selected_repos, id)
      else
        MapSet.put(socket.assigns.selected_repos, id)
      end

    # Get the selected repositories
    selected_repositories = Enum.filter(repositories, &MapSet.member?(selected_repos, &1.id))

    # Get unique folders from selected repositories
    target_folders =
      selected_repositories
      |> Enum.map(& &1.folder)
      |> Enum.reject(&is_nil/1)
      |> Enum.uniq()

    finalize(:repository_sync, %{
      selected_repos: selected_repositories,
      target_folders: target_folders
    })

    {:noreply,
     socket
     |> assign(:selected_repos, selected_repos)}
  end

  def handle_event("filter", %{"value" => filter}, socket) do
    {:noreply, socket |> assign(:repository_filter, filter) |> maybe_filter_repos()}
  end

  defp maybe_filter_repos(socket) do
    keyword = String.downcase(socket.assigns.repository_filter)

    # Filter the already grouped repos assigned by `assign_repositories`
    unorganized_repos =
      Enum.filter(
        socket.assigns.unorganized_repos || [],
        &String.contains?(String.downcase(&1.name), keyword)
      )

    repos_by_folder =
      socket.assigns.repos_by_folder
      |> Enum.map(fn {folder_id, repos} ->
        filtered = Enum.filter(repos, &String.contains?(String.downcase(&1.name), keyword))
        {folder_id, filtered}
      end)
      |> Enum.into(%{})

    # Re-assign the filtered versions
    socket
    |> assign(:unorganized_repos, unorganized_repos)
    |> assign(:repos_by_folder, repos_by_folder)
  end
end
