defmodule Repobot.FilesTest do
  use Repobot.DataCase

  alias Repobot.Files
  import Repobot.Test.Fixtures

  describe "find_common_files/2" do
    test "finds common files across repositories" do
      user = create_user()

      # Create two repositories with similar files
      repo1 = create_repository(%{name: "repo1", user_id: user.id})
      repo2 = create_repository(%{name: "repo2", user_id: user.id})

      # Add identical files in root
      create_repository_file(%{
        repository_id: repo1.id,
        path: "README.md",
        content: "# Test Repository",
        size: 100
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "README.md",
        content: "# Different Content",
        size: 100
      })

      # Add identical files in nested directories
      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/test.ex",
        content: "defmodule Test do\n  def hello, do: :world\nend",
        size: 200
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/test.ex",
        content: "defmodule Test do\n  def hello, do: :earth\nend",
        size: 200
      })

      # Add a file that exists only in one repository
      create_repository_file(%{
        repository_id: repo1.id,
        path: "unique.ex",
        content: "# Unique file",
        size: 400
      })

      # Reload repositories with files
      repo1 = Repobot.Repo.preload(repo1, :files)
      repo2 = Repobot.Repo.preload(repo2, :files)

      {:ok, common_files} = Files.find_common_files([repo1, repo2])

      # Should find 2 common files: README.md and lib/test.ex
      assert length(common_files) == 2

      # Files should be sorted by count and then by path
      assert Enum.map(common_files, & &1["path"]) == ["README.md", "lib/test.ex"]

      # Verify file information
      readme = Enum.find(common_files, &(&1["path"] == "README.md"))
      assert readme["count"] == 2
      assert readme["size"] == 100
      assert readme["total_repos"] == 2

      test_ex = Enum.find(common_files, &(&1["path"] == "lib/test.ex"))
      assert test_ex["count"] == 2
      assert test_ex["size"] == 200
      assert test_ex["total_repos"] == 2
    end

    test "handles repositories with no common files" do
      user = create_user()

      # Create two repositories with different files
      repo1 = create_repository(%{name: "repo1", user_id: user.id})
      repo2 = create_repository(%{name: "repo2", user_id: user.id})

      create_repository_file(%{
        repository_id: repo1.id,
        path: "file1.txt",
        content: "Content 1",
        size: 100
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "file2.txt",
        content: "Content 2",
        size: 100
      })

      repo1 = Repobot.Repo.preload(repo1, :files)
      repo2 = Repobot.Repo.preload(repo2, :files)

      {:ok, common_files} = Files.find_common_files([repo1, repo2])
      assert Enum.empty?(common_files)
    end

    test "handles empty repositories" do
      user = create_user()

      # Create two empty repositories
      repo1 = create_repository(%{name: "repo1", user_id: user.id})
      repo2 = create_repository(%{name: "repo2", user_id: user.id})

      repo1 = Repobot.Repo.preload(repo1, :files)
      repo2 = Repobot.Repo.preload(repo2, :files)

      {:ok, common_files} = Files.find_common_files([repo1, repo2])
      assert Enum.empty?(common_files)
    end

    test "respects min_repos option" do
      user = create_user()

      # Create three repositories with some common files
      repo1 = create_repository(%{name: "repo1", user_id: user.id})
      repo2 = create_repository(%{name: "repo2", user_id: user.id})
      repo3 = create_repository(%{name: "repo3", user_id: user.id})

      # File present in all three repos
      Enum.each([repo1, repo2, repo3], fn repo ->
        create_repository_file(%{
          repository_id: repo.id,
          path: "common.txt",
          content: "content",
          size: 100
        })
      end)

      # File present in only two repos
      Enum.each([repo1, repo2], fn repo ->
        create_repository_file(%{
          repository_id: repo.id,
          path: "partial.txt",
          content: "content",
          size: 100
        })
      end)

      repos = [repo1, repo2, repo3] |> Enum.map(&Repo.preload(&1, :files))

      # With min_repos: 2, should find both files
      {:ok, common_files} = Files.find_common_files(repos, min_repos: 2)
      assert length(common_files) == 2

      # With min_repos: 3, should only find the file present in all repos
      {:ok, common_files} = Files.find_common_files(repos, min_repos: 3)
      assert length(common_files) == 1
      assert hd(common_files)["path"] == "common.txt"
    end

    test "excludes .gitkeep files regardless of directory" do
      user = create_user()
      repo1 = create_repository(%{name: "repo1", user_id: user.id})
      repo2 = create_repository(%{name: "repo2", user_id: user.id})

      # Add .gitkeep files in various directories
      [
        "lib/.gitkeep",
        "test/.gitkeep",
        "priv/static/.gitkeep",
        "assets/js/.gitkeep"
      ]
      |> Enum.each(fn path ->
        create_repository_file(%{
          repository_id: repo1.id,
          path: path,
          content: "",
          size: 0
        })

        create_repository_file(%{
          repository_id: repo2.id,
          path: path,
          content: "",
          size: 0
        })
      end)

      # Add a real file to verify the function still works
      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/app.ex",
        content: "content",
        size: 100
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/app.ex",
        content: "content",
        size: 100
      })

      repo1 = Repobot.Repo.preload(repo1, :files)
      repo2 = Repobot.Repo.preload(repo2, :files)

      {:ok, common_files} = Files.find_common_files([repo1, repo2])

      # Should only find app.ex, no .gitkeep files
      assert length(common_files) == 1
      assert hd(common_files)["path"] == "lib/app.ex"
    end

    test "excludes files based on default exclusions" do
      user = create_user()
      repo1 = create_repository(%{name: "repo1", user_id: user.id})
      repo2 = create_repository(%{name: "repo2", user_id: user.id})

      # Add various excluded files
      [
        ".DS_Store",
        "mix.lock",
        "package-lock.json",
        "deps/phoenix/mix.exs",
        "node_modules/react/package.json",
        "priv/static/app.js",
        "_build/dev/lib/phoenix/ebin/Elixir.Phoenix.beam",
        ".vscode/settings.json",
        "logs/error.log"
      ]
      |> Enum.each(fn path ->
        create_repository_file(%{
          repository_id: repo1.id,
          path: path,
          content: "content",
          size: 100
        })

        create_repository_file(%{
          repository_id: repo2.id,
          path: path,
          content: "content",
          size: 100
        })
      end)

      # Add a real file to verify the function still works
      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/app.ex",
        content: "content",
        size: 100
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/app.ex",
        content: "content",
        size: 100
      })

      repo1 = Repobot.Repo.preload(repo1, :files)
      repo2 = Repobot.Repo.preload(repo2, :files)

      {:ok, common_files} = Files.find_common_files([repo1, repo2])

      # Should only find app.ex, no excluded files
      assert length(common_files) == 1
      assert hd(common_files)["path"] == "lib/app.ex"
    end

    test "allows custom exclusions" do
      user = create_user()
      repo1 = create_repository(%{name: "repo1", user_id: user.id})
      repo2 = create_repository(%{name: "repo2", user_id: user.id})

      # Add files that we'll exclude with custom patterns
      [
        "custom.lock",
        "tmp/cache.json",
        "spec/test.spec"
      ]
      |> Enum.each(fn path ->
        create_repository_file(%{
          repository_id: repo1.id,
          path: path,
          content: "content",
          size: 100
        })

        create_repository_file(%{
          repository_id: repo2.id,
          path: path,
          content: "content",
          size: 100
        })
      end)

      # Add a real file to verify the function still works
      create_repository_file(%{
        repository_id: repo1.id,
        path: "lib/app.ex",
        content: "content",
        size: 100
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "lib/app.ex",
        content: "content",
        size: 100
      })

      repo1 = Repobot.Repo.preload(repo1, :files)
      repo2 = Repobot.Repo.preload(repo2, :files)

      {:ok, common_files} =
        Files.find_common_files([repo1, repo2],
          exclusions: ["custom.lock", "tmp/", "*.spec"]
        )

      # Should only find app.ex, no excluded files
      assert length(common_files) == 1
      assert hd(common_files)["path"] == "lib/app.ex"
    end
  end

  describe "calculate_similarity/1" do
    test "returns 100 for identical contents" do
      content = "test content"
      assert Files.calculate_similarity([content, content, content]) == 100
    end

    test "returns 100 for single content" do
      assert Files.calculate_similarity(["test content"]) == 100
    end

    test "calculates similarity for multiple different contents" do
      content1 = "line1\nline2\nline3"
      content2 = "line1\nline2\ndifferent"
      content3 = "line1\ndifferent\nline3"

      similarity = Files.calculate_similarity([content1, content2, content3])
      assert similarity > 0 and similarity < 100
    end

    test "handles empty contents" do
      assert Files.calculate_similarity(["", ""]) == 100
    end
  end

  describe "calculate_pair_similarity/2" do
    test "returns 100 for identical contents" do
      content = "test content"
      assert Files.calculate_pair_similarity(content, content) == 100
    end

    test "returns 0 for nil contents" do
      assert Files.calculate_pair_similarity(nil, "content") == 0
      assert Files.calculate_pair_similarity("content", nil) == 0
      assert Files.calculate_pair_similarity(nil, nil) == 0
    end

    test "calculates line-by-line similarity" do
      content1 = "line1\nline2\nline3"
      content2 = "line1\nline2\ndifferent"
      similarity = Files.calculate_pair_similarity(content1, content2)
      # 2 out of 3 lines match
      assert similarity == 66
    end

    test "handles empty contents" do
      assert Files.calculate_pair_similarity("", "") == 100
    end

    test "handles different line endings" do
      content1 = "line1\nline2\nline3"
      content2 = "line1\r\nline2\r\nline3"
      assert Files.calculate_pair_similarity(content1, content2) == 100
    end
  end

  describe "calculate_common_files_similarity/3" do
    test "calculates similarity with progress updates" do
      user = create_user()

      # Create two repositories with similar files
      repo1 = create_repository(%{name: "repo1", user_id: user.id})
      repo2 = create_repository(%{name: "repo2", user_id: user.id})

      # Add files with different content
      create_repository_file(%{
        repository_id: repo1.id,
        path: "test.ex",
        content: "defmodule Test do\n  def hello, do: :world\nend",
        size: 100
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "test.ex",
        content: "defmodule Test do\n  def hello, do: :earth\nend",
        size: 100
      })

      # Preload files
      repo1 = Repo.preload(repo1, :files)
      repo2 = Repo.preload(repo2, :files)

      # Get common files first
      {:ok, common_files} = Files.find_common_files([repo1, repo2])
      assert length(common_files) == 1

      # Calculate similarity
      assert :ok = Files.calculate_common_files_similarity(common_files, [repo1, repo2], self())

      # Should receive progress updates and completion with files
      assert_receive {:similarity_progress, progress} when progress > 0
      assert_receive {:similarity_complete, [file]}
      assert file["path"] == "test.ex"
      assert file["similarity"] > 0
    end

    test "handles empty files list" do
      assert :ok = Files.calculate_common_files_similarity([], [], self())
      assert_receive {:similarity_complete, []}
    end

    test "handles missing content" do
      user = create_user()

      # Create repositories with files but no content
      repo1 = create_repository(%{name: "repo1", user_id: user.id})
      repo2 = create_repository(%{name: "repo2", user_id: user.id})

      create_repository_file(%{
        repository_id: repo1.id,
        path: "test.ex",
        content: nil,
        size: 100
      })

      create_repository_file(%{
        repository_id: repo2.id,
        path: "test.ex",
        content: nil,
        size: 100
      })

      repo1 = Repo.preload(repo1, :files)
      repo2 = Repo.preload(repo2, :files)

      {:ok, common_files} = Files.find_common_files([repo1, repo2])
      assert :ok = Files.calculate_common_files_similarity(common_files, [repo1, repo2], self())

      # Should complete with empty list since no files had content
      assert_receive {:similarity_complete, []}
    end
  end
end
